{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src", "format": "prettier --write ./src"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.1", "react": "^19.0.0", "react-admin": "^5.10.0", "react-dom": "^19.0.0", "react-router": "^7.1.3", "react-router-dom": "^7.1.3"}, "devDependencies": {"@eslint/js": "^9.23.0", "@types/node": "^20.10.7", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.0.0", "prettier": "^3.3.3", "typescript": "^5.1.6", "typescript-eslint": "^8.28.0", "vite": "^6.2.6"}, "name": "dashboard"}