import type { ReactNode } from "react";
import { Layout as RALayout, CheckForApplicationUpdate } from "react-admin";
import { Box, Typography, Button } from "@mui/material";
import { useState } from "react";
import { createClient } from "shared/lib/supabase/client";

export const Layout = ({ children }: { children: ReactNode }) => {
  const [testResult, setTestResult] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const testSupabaseConnection = async () => {
    setLoading(true);
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .schema("app_test")
        .rpc("test_request");

      if (error) {
        setTestResult(`Error: ${error.message}`);
      } else {
        setTestResult(`Success: ${data}`);
      }
    } catch (err) {
      setTestResult(
        `Exception: ${err instanceof Error ? err.message : String(err)}`,
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <RALayout>
      <Box
        sx={{
          p: 2,
          backgroundColor: "#f5f5f5",
          borderBottom: "1px solid #ddd",
        }}
      >
        <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
          Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Button
            variant="contained"
            size="small"
            onClick={testSupabaseConnection}
            disabled={loading}
          >
            {loading ? "Testing..." : "Test Supabase Connection"}
          </Button>
          {testResult && (
            <Typography
              variant="body2"
              color={
                testResult.startsWith("Success") ? "success.main" : "error.main"
              }
            >
              {testResult}
            </Typography>
          )}
        </Box>
      </Box>
      {children}
      <CheckForApplicationUpdate />
    </RALayout>
  );
};
