{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "dev",
      "dependsOn": ["_api", "_dashboard"],
      "group": {
        "kind": "none"
      },
      "problemMatcher": [],
      "runOptions": {
        "runOn": "folderOpen"
      }
    },
    {
      "label": "_api",
      "type": "shell",
      "command": "pnpm --filter api dev",
      "isBackground": true,
      "problemMatcher": {
        "pattern": {
          "regexp": "Ready"
        },
        "background": {
          "activeOnStart": true,
          "beginsPattern": "Starting",
          "endsPattern": "Ready"
        }
      }
      // "presentation": {
      //   "reveal": "never"
      // }
    },
    {
      "label": "_webapp",
      "type": "shell",
      "command": "pnpm --filter webapp dev-localhost",
      "isBackground": true,
      "problemMatcher": {
        "pattern": {
          "regexp": "Ready"
        },
        "background": {
          "activeOnStart": true,
          "beginsPattern": "Starting",
          "endsPattern": "Ready"
        }
      }
      // "presentation": {
      //   "reveal": "never"
      // }
    },
    {
      "label": "_dashboard",
      "type": "shell",
      "command": "pnpm --filter dashboard dev",
      "isBackground": true,
      "problemMatcher": {
        "pattern": {
          "regexp": "ready in"
        },
        "background": {
          "activeOnStart": true,
          "beginsPattern": "ready in",
          "endsPattern": "ready in"
        }
      }
      // "presentation": {
      //   "reveal": "never"
      // }
    }
  ]
}
