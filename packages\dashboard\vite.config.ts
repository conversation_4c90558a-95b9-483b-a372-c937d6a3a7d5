import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { load } from "dotenv-mono";
import path from "path";

load();

const requiredEnvVars = [
  "NEXT_PUBLIC_SUPABASE_URL",
  "NEXT_PUBLIC_SUPABASE_ANON_KEY",
];

requiredEnvVars.forEach((envVar) => {
  if (!process.env[envVar]) {
    console.log(`Environment variable ${envVar} is not defined.`);
    throw new Error();
  }
});

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [react()],
  server: {
    host: true,
  },
  build: {
    sourcemap: mode === "development",
  },
  base: "./",
  resolve: {
    alias: {
      shared: path.resolve(__dirname, "../shared"),
    },
  },
  define: {
    "process.env.NEXT_PUBLIC_SUPABASE_URL": JSON.stringify(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
    ),
    "process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY": JSON.stringify(
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    ),
  },
}));
