import { Card, CardContent, Typography, <PERSON>, Button } from "@mui/material";
import { useState } from "react";
import { createClient } from "shared/lib/supabase/client";

export const Dashboard = () => {
  const [testResult, setTestResult] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const testSupabaseConnection = async () => {
    setLoading(true);
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .schema("app_test")
        .rpc("test_request");
      
      if (error) {
        setTestResult(`Error: ${error.message}`);
      } else {
        setTestResult(`Success: ${data}`);
      }
    } catch (err) {
      setTestResult(
        `Exception: ${err instanceof Error ? err.message : String(err)}`,
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        E-Senpai Dashboard
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Environment Information
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Supabase Anon Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...
          </Typography>
        </CardContent>
      </Card>

      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Supabase Connection Test
          </Typography>
          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Test the connection to Supabase by calling the app_test.test_request() function.
          </Typography>
          
          <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
            <Button
              variant="contained"
              onClick={testSupabaseConnection}
              disabled={loading}
            >
              {loading ? "Testing..." : "Test Supabase Connection"}
            </Button>
          </Box>

          {testResult && (
            <Box
              sx={{
                p: 2,
                borderRadius: 1,
                backgroundColor: testResult.startsWith("Success") 
                  ? "success.light" 
                  : "error.light",
                color: testResult.startsWith("Success") 
                  ? "success.contrastText" 
                  : "error.contrastText",
              }}
            >
              <Typography variant="body2">
                {testResult}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};
